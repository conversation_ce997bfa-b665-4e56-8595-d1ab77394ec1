# 🚀 Kubernetes Infrastructure Deployment

Bộ script tự động triển khai hạ tầng Kubernetes hoàn chỉnh với các dịch vụ cốt lõi:

- **🌐 Traefik** - API Gateway với HTTPS tự động và basic auth
- **🗄️ PostgreSQL** - Database với multi-environment support  
- **💾 MinIO** - Object Storage với bucket riêng cho từng môi trường
- **🔐 Keycloak** - Single Sign-On với realm và client được cấu hình sẵn

## ⚡ Quick Start

```bash
# Triển khai môi trường dev
make deploy-dev

# Kiểm tra trạng thái
make test-dev

# Xem credentials
make show-creds ENV=dev
```

📖 **[Xem hướng dẫn chi tiết](QUICKSTART.md)**

## 🏗️ Kiến trúc

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│   Environment   │    │   Environment   │    │   Environment   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
              ┌─────────────────────────────────────┐
              │        Shared Services              │
              │  ┌─────────────────────────────┐    │
              │  │         Traefik             │    │
              │  │      (API Gateway)          │    │
              │  └─────────────────────────────┘    │
              │  ┌─────────────────────────────┐    │
              │  │       PostgreSQL            │    │
              │  │   (dev/staging/prod DBs)    │    │
              │  └─────────────────────────────┘    │
              │  ┌─────────────────────────────┐    │
              │  │         MinIO               │    │
              │  │  (env-specific buckets)     │    │
              │  └─────────────────────────────┘    │
              │  ┌─────────────────────────────┐    │
              │  │        Keycloak             │    │
              │  │   (shared realm, env        │    │
              │  │    specific clients)        │    │
              │  └─────────────────────────────┘    │
              └─────────────────────────────────────┘
```

## 📋 Danh sách tài khoản quản trị

Sau khi triển khai, bạn sẽ nhận được file `credentials-{env}.txt` chứa:

### 🗄️ PostgreSQL
- **Host**: `postgres-service.default.svc.cluster.local:5432`
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên 20 ký tự>`
- **Databases**: `dev`, `staging`, `prod`, `keycloak`

### 💾 MinIO
- **Access Key**: `admin`  
- **Secret Key**: `<tạo ngẫu nhiên 20 ký tự>`
- **Buckets**: `dev-bucket`, `staging-bucket`, `prod-bucket`

### 🔐 Keycloak
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên 20 ký tự>`
- **Realm**: `shared-realm`
- **Clients**: `cms-client`, `api-client`, `frontend-client`

### 🌐 Traefik
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên 16 ký tự>`

## 🔗 Địa chỉ dịch vụ

### Development
```
🌐 Traefik Dashboard: https://traefik-dev.yourdomain.com
💾 MinIO Console:     https://minio-dev.yourdomain.com
🔌 MinIO API:         https://minio-api-dev.yourdomain.com
🔐 Keycloak:          https://keycloak-dev.yourdomain.com
```

### Staging  
```
🌐 Traefik Dashboard: https://traefik-staging.yourdomain.com
💾 MinIO Console:     https://minio-staging.yourdomain.com
🔌 MinIO API:         https://minio-api-staging.yourdomain.com
🔐 Keycloak:          https://keycloak-staging.yourdomain.com
```

### Production
```
🌐 Traefik Dashboard: https://traefik.yourdomain.com
💾 MinIO Console:     https://minio.yourdomain.com  
🔌 MinIO API:         https://minio-api.yourdomain.com
🔐 Keycloak:          https://keycloak.yourdomain.com
```

## 🛠️ Các lệnh hữu ích

```bash
# Triển khai
make deploy ENV=dev          # Triển khai môi trường cụ thể
make deploy-staging          # Triển khai staging
make deploy-prod            # Triển khai production

# Kiểm tra
make test ENV=dev           # Test môi trường cụ thể  
make status                 # Hiển thị trạng thái cluster
make pods ENV=dev           # Hiển thị pods trong namespace

# Quản lý
make clean ENV=dev          # Xóa môi trường cụ thể
make clean-all             # Xóa tất cả môi trường
make update-creds ENV=dev   # Cập nhật credentials

# Debug
make logs-traefik          # Xem logs Traefik
make logs-postgres         # Xem logs PostgreSQL
make port-forward-postgres # Port forward PostgreSQL
```

## 📁 Cấu trúc project

```
k8s-deployment/
├── infrastructure/
│   ├── base/                    # Base deployments
│   ├── environments/            # Environment-specific configs
│   │   ├── dev/
│   │   ├── staging/
│   │   └── prod/
│   └── scripts/                 # Deployment scripts
├── cms/                         # CMS application (future)
├── Makefile                     # Quick commands
├── QUICKSTART.md               # Hướng dẫn nhanh
└── README.md                   # File này
```

## ⚙️ Yêu cầu hệ thống

- **Kubernetes cluster** (v1.20+)
- **kubectl** configured
- **PowerShell** (Windows) hoặc **pwsh** (Linux/macOS)
- **Domain name** với DNS pointing to your cluster

## 🔧 Cấu hình

1. **Cập nhật domain**: Thay `yourdomain.com` trong các file config
2. **Cập nhật email**: Thay email Let's Encrypt trong `base/traefik.yaml`
3. **Chạy deployment**: `make deploy-dev`

## 🆘 Hỗ trợ

- **📖 Documentation**: [infrastructure/README.md](infrastructure/README.md)
- **🚀 Quick Start**: [QUICKSTART.md](QUICKSTART.md)  
- **🐛 Issues**: Sử dụng `make logs-*` commands để debug

---

🎯 **Mục tiêu**: Triển khai hạ tầng Kubernetes production-ready trong vòng 5 phút!