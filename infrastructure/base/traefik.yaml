apiVersion: v1
kind: ServiceAccount
metadata:
  name: traefik
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: traefik
rules:
  - apiGroups:
      - ""
    resources:
      - services
      - endpoints
      - secrets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
      - ingressclasses
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses/status
    verbs:
      - update
  - apiGroups:
      - traefik.io
    resources:
      - middlewares
      - middlewaretcps
      - ingressroutes
      - traefikservices
      - ingressroutetcps
      - ingressrouteudps
      - tlsoptions
      - tlsstores
      - serverstransports
      - serverstransporttcps
    verbs:
      - get
      - list
      - watch
    verbs:
      - get
      - list
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: traefik
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: traefik
subjects:
  - kind: ServiceAccount
    name: traefik
    namespace: default
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: traefik-config
  namespace: default
data:
  traefik.yml: |
    global:
      checkNewVersion: false
      sendAnonymousUsage: false

    api:
      dashboard: true
      debug: true
      insecure: true
      disableDashboardAd: true

    ping: {}

    entryPoints:
      web:
        address: ":80"
      websecure:
        address: ":443"

    certificatesResolvers:
      letsencrypt:
        acme:
          email: <EMAIL>
          storage: /data/acme.json
          httpChallenge:
            entryPoint: web

    providers:
      kubernetesIngress: {}
      kubernetesCRD:
        allowCrossNamespace: true

    log:
      level: INFO
      filePath: /var/log/traefik/traefik.log

    accessLog:
      filePath: /var/log/traefik/access.log
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: traefik
  namespace: default
  labels:
    app: traefik
spec:
  replicas: 1
  selector:
    matchLabels:
      app: traefik
  template:
    metadata:
      labels:
        app: traefik
    spec:
      serviceAccountName: traefik
      containers:
        - name: traefik
          image: traefik:v3.0
          args:
            - --configfile=/config/traefik.yml
          ports:
            - name: web
              containerPort: 80
            - name: websecure
              containerPort: 443
            - name: dashboard
              containerPort: 8080
          volumeMounts:
            - name: config
              mountPath: /config
            - name: data
              mountPath: /data
            - name: logs
              mountPath: /var/log/traefik
          livenessProbe:
            httpGet:
              path: /ping
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /ping
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: config
          configMap:
            name: traefik-config
        - name: data
          persistentVolumeClaim:
            claimName: traefik-data
        - name: logs
          emptyDir: {}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: traefik-data
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: traefik
  namespace: default
  labels:
    app: traefik
spec:
  type: LoadBalancer
  ports:
    - name: web
      port: 80
      targetPort: 80
    - name: websecure
      port: 443
      targetPort: 443
  selector:
    app: traefik
---
apiVersion: v1
kind: Service
metadata:
  name: traefik-nodeport
  namespace: default
  labels:
    app: traefik
spec:
  type: NodePort
  ports:
    - name: web
      port: 80
      targetPort: 80
      nodePort: 32032
  selector:
    app: traefik
---
apiVersion: v1
kind: Service
metadata:
  name: traefik-dashboard
  namespace: default
  labels:
    app: traefik
spec:
  ports:
    - name: dashboard
      port: 8080
      targetPort: 8080
  selector:
    app: traefik
