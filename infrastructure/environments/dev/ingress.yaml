apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: traefik-dashboard-cms-dev
  namespace: cms-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`traefik-dev.osp.vn`)
      kind: Rule
      middlewares:
        - name: dashboard-auth
          namespace: default
      services:
        - name: traefik-dashboard
          namespace: default
          port: 8080
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: minio-console-cms-dev
  namespace: cms-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`minio-dev.osp.vn`)
      kind: Rule
      services:
        - name: minio-console-service
          namespace: default
          port: 9001
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: minio-api-cms-dev
  namespace: cms-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`minio-api-dev.osp.vn`)
      kind: Rule
      services:
        - name: minio-api-service
          namespace: default
          port: 9000
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: keycloak-cms-dev
  namespace: cms-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`keycloak-dev.osp.vn`)
      kind: Rule
      middlewares:
        - name: keycloak-headers
          namespace: default
      services:
        - name: keycloak-service
          namespace: default
          port: 8080
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: pgadmin-cms-dev
  namespace: cms-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`pgadmin-dev.osp.vn`)
      kind: Rule
      middlewares:
        - name: pgadmin-headers
          namespace: default
      services:
        - name: pgadmin-service
          namespace: default
          port: 80
  tls:
    certResolver: letsencrypt
---
# HTTP-based routes for direct IP access
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: minio-console-http
  namespace: cms-dev
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/minio-console`)
      kind: Rule
      services:
        - name: minio-console-service
          namespace: default
          port: 9001
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: minio-api-http
  namespace: cms-dev
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/minio-api`)
      kind: Rule
      services:
        - name: minio-api-service
          namespace: default
          port: 9000
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: keycloak-http
  namespace: cms-dev
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/keycloak`)
      kind: Rule
      middlewares:
        - name: keycloak-headers
          namespace: default
      services:
        - name: keycloak-service
          namespace: default
          port: 8080
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: pgadmin-http
  namespace: cms-dev
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/pgadmin`)
      kind: Rule
      middlewares:
        - name: pgadmin-headers
          namespace: default
      services:
        - name: pgadmin-service
          namespace: default
          port: 80
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: portainer-http
  namespace: cms-dev
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/portainer`)
      kind: Rule
      middlewares:
        - name: portainer-stripprefix
          namespace: portainer
      services:
        - name: portainer-service
          namespace: portainer
          port: 9000
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: portainer-cms-dev
  namespace: cms-dev
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`portainer-dev.osp.vn`)
      kind: Rule
      services:
        - name: portainer-service
          namespace: portainer
          port: 9000
  tls:
    certResolver: letsencrypt

